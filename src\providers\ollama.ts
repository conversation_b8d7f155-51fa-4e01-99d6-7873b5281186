import axios, { AxiosInstance } from 'axios';
import { <PERSON><PERSON>rovider, AgentMessage, AgentConfig, Tool, ToolCall } from '@/types';
import { logger } from '@/utils/logger';

export class OllamaProvider implements LLMProvider {
  public readonly name = 'ollama';
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      timeout: 120000, // Ollama can be slower
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  public validateConfig(config: AgentConfig): boolean {
    return !!(config.baseUrl && config.model);
  }

  public async sendMessage(messages: AgentMessage[], config: AgentConfig): Promise<string> {
    try {
      // First check if model is available
      await this.ensureModelAvailable(config);

      const response = await this.client.post(
        `${config.baseUrl}/api/chat`,
        {
          model: config.model,
          messages: this.formatMessages(messages),
          stream: false,
          options: {
            temperature: config.temperature ?? 0.7,
            num_predict: config.maxTokens ?? 4000,
          },
        }
      );

      const content = (response.data as { message?: { content?: string }; done?: boolean }).message?.content;
      if (!content) {
        throw new Error('No content in response');
      }

      logger.debug('Ollama response received', {
        model: config.model,
        done: (response.data as { done?: boolean }).done
      }, 'OllamaProvider');

      return content;
    } catch (error: unknown) {
      logger.error('Ollama API error', error, 'OllamaProvider');

      const nodeError = error as { code?: string; response?: { status?: number }; message?: string };
      if (nodeError.code === 'ECONNREFUSED') {
        throw new Error('Cannot connect to Ollama. Make sure Ollama is running.');
      }

      if (nodeError.response?.status === 404) {
        throw new Error(`Model ${config.model} not found in Ollama`);
      }

      const errorMessage = nodeError.message ?? 'Unknown error';
      throw new Error(`Ollama API error: ${errorMessage}`);
    }
  }

  public async sendToolMessage(
    messages: AgentMessage[], 
    tools: Tool[], 
    config: AgentConfig
  ): Promise<{ message: string; toolCalls?: ToolCall[] }> {
    try {
      await this.ensureModelAvailable(config);

      // Ollama doesn't have native tool calling, so we'll use a prompt-based approach
      const toolsPrompt = this.createToolsPrompt(tools);
      const enhancedMessages = [
        {
          role: 'system' as const,
          content: toolsPrompt,
        },
        ...messages,
      ];

      const response = await this.client.post(
        `${config.baseUrl}/api/chat`,
        {
          model: config.model,
          messages: this.formatMessages(enhancedMessages),
          stream: false,
          options: {
            temperature: config.temperature ?? 0.7,
            num_predict: config.maxTokens ?? 4000,
          },
        }
      );

      const content = (response.data as { message?: { content?: string } }).message?.content;
      if (!content) {
        throw new Error('No content in response');
      }

      // Parse tool calls from the response
      const { message, toolCalls } = this.parseToolCalls(content);

      logger.debug('Ollama tool response received', {
        model: config.model,
        hasToolCalls: toolCalls.length > 0,
        toolCallsCount: toolCalls.length
      }, 'OllamaProvider');

      const result: { message: string; toolCalls?: ToolCall[] } = {
        message,
      };

      if (toolCalls.length > 0) {
        result.toolCalls = toolCalls;
      }

      return result;
    } catch (error: unknown) {
      logger.error('Ollama tool API error', error, 'OllamaProvider');

      const nodeError = error as { code?: string; response?: { status?: number }; message?: string };
      if (nodeError.code === 'ECONNREFUSED') {
        throw new Error('Cannot connect to Ollama. Make sure Ollama is running.');
      }

      if (nodeError.response?.status === 404) {
        throw new Error(`Model ${config.model} not found in Ollama`);
      }

      const errorMessage = nodeError.message ?? 'Unknown error';
      throw new Error(`Ollama API error: ${errorMessage}`);
    }
  }

  private async ensureModelAvailable(config: AgentConfig): Promise<void> {
    try {
      const response = await this.client.get(`${config.baseUrl}/api/tags`);
      const models = (response.data as { models?: Array<{ name: string }> }).models ?? [];
      const modelExists = models.some((model) => model.name === config.model);

      if (!modelExists) {
        logger.info(`Model ${config.model} not found, attempting to pull...`, undefined, 'OllamaProvider');
        await this.pullModel(config);
      }
    } catch (error) {
      logger.warn('Could not check model availability', error, 'OllamaProvider');
    }
  }

  private async pullModel(config: AgentConfig): Promise<void> {
    try {
      await this.client.post(`${config.baseUrl}/api/pull`, {
        name: config.model,
      });
      logger.info(`Successfully pulled model ${config.model}`, undefined, 'OllamaProvider');
    } catch (error) {
      throw new Error(`Failed to pull model ${config.model}: ${error}`);
    }
  }

  private formatMessages(messages: AgentMessage[]): unknown[] {
    return messages.map(msg => ({
      role: msg.role === 'tool' ? 'assistant' : msg.role,
      content: msg.content,
    }));
  }

  private createToolsPrompt(tools: Tool[]): string {
    const toolDescriptions = tools.map(tool => {
      return `Tool: ${tool.name}
Description: ${tool.description}
Parameters: ${JSON.stringify(this.zodToJsonSchema(tool.parameters), null, 2)}`;
    }).join('\n\n');

    return `You are an AI assistant with access to tools. When you need to use a tool, respond with a JSON object in this exact format:

{
  "tool_calls": [
    {
      "id": "unique_id",
      "type": "function",
      "function": {
        "name": "tool_name",
        "arguments": "{\"param1\": \"value1\"}"
      }
    }
  ]
}

Available tools:
${toolDescriptions}

If you don't need to use any tools, respond normally without the JSON format.`;
  }

  private parseToolCalls(content: string): { message: string; toolCalls: ToolCall[] } {
    try {
      // Try to find JSON in the response
      const jsonMatch = content.match(/\{[\s\S]*"tool_calls"[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]) as { tool_calls?: unknown[] };
        if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
          return {
            message: content.replace(jsonMatch[0], '').trim(),
            toolCalls: parsed.tool_calls.map((call: unknown, index: number) => {
              const toolCall = call as { id?: string; function: { name: string; arguments: string } };
              return {
                id: toolCall.id ?? `call_${Date.now()}_${index}`,
                type: 'function' as const,
                function: {
                  name: toolCall.function.name,
                  arguments: toolCall.function.arguments,
                },
              };
            }),
          };
        }
      }
    } catch (error) {
      logger.debug('Failed to parse tool calls from response', error, 'OllamaProvider');
    }

    return {
      message: content,
      toolCalls: [],
    };
  }

  private zodToJsonSchema(schema: unknown): unknown {
    // Basic Zod to JSON Schema conversion
    const zodSchema = schema as { _def?: { typeName?: string; shape?: () => Record<string, unknown>; type?: unknown; innerType?: unknown; optional?: boolean } };

    if (zodSchema._def?.typeName === 'ZodObject') {
      const properties: Record<string, unknown> = {};
      const required: string[] = [];

      const shape = zodSchema._def.shape?.();
      if (shape) {
        for (const [key, value] of Object.entries(shape)) {
          properties[key] = this.zodToJsonSchema(value);
          const valueSchema = value as { _def?: { optional?: boolean } };
          if (!valueSchema._def?.optional) {
            required.push(key);
          }
        }
      }

      return {
        type: 'object',
        properties,
        required: required.length > 0 ? required : undefined,
      };
    }

    if (zodSchema._def?.typeName === 'ZodString') {
      return { type: 'string' };
    }

    if (zodSchema._def?.typeName === 'ZodNumber') {
      return { type: 'number' };
    }

    if (zodSchema._def?.typeName === 'ZodBoolean') {
      return { type: 'boolean' };
    }

    if (zodSchema._def?.typeName === 'ZodArray') {
      return {
        type: 'array',
        items: this.zodToJsonSchema(zodSchema._def.type),
      };
    }

    if (zodSchema._def?.typeName === 'ZodOptional') {
      return this.zodToJsonSchema(zodSchema._def.innerType);
    }

    return { type: 'string' }; // fallback
  }
}
