import { spawn } from 'cross-spawn';
import { ChildProcess } from 'child_process';
import { EventEmitter } from 'events';

import { ExecutionContext } from '@/types';
import { logger } from '@/utils/logger';
import { config } from '@/config';

export interface ShellExecutionResult {
  success: boolean;
  message: string;
  data?: {
    stdout: string;
    stderr: string;
    exitCode: number;
    command: string;
    workingDirectory: string;
    duration: number;
    pid?: number | undefined;
  };
  error?: string;
}

export interface ShellExecutionOptions {
  timeout?: number;
  cwd?: string;
  env?: Record<string, string>;
  shell?: boolean;
  detached?: boolean;
  stdio?: 'pipe' | 'inherit' | 'ignore';
}

export class ShellOperations extends EventEmitter {
  private static instance: ShellOperations;
  private runningProcesses: Map<number, ChildProcess> = new Map();

  public static getInstance(): ShellOperations {
    if (!ShellOperations.instance) {
      ShellOperations.instance = new ShellOperations();
    }
    return ShellOperations.instance;
  }

  public async executeCommand(
    command: string,
    context: ExecutionContext,
    options: ShellExecutionOptions = {}
  ): Promise<ShellExecutionResult> {
    try {
      this.validateCommand(command, context);

      const {
        timeout = 30000, // 30 seconds default
        cwd = context.workingDirectory,
        env = { ...process.env, ...context.environment },
        shell = true,
        detached = false,
        stdio = 'pipe',
      } = options;

      logger.info(`Executing command: ${command}`, { 
        cwd, 
        timeout,
        detached 
      }, 'ShellOperations', context.sessionId);

      const startTime = Date.now();
      
      return new Promise((resolve) => {
        let stdout = '';
        let stderr = '';
        let isResolved = false;

        // Parse command and arguments
        const [cmd, ...args] = this.parseCommand(command);

        const childProcess = spawn(cmd!, args, {
          cwd,
          env,
          shell,
          detached,
          stdio,
        });

        if (childProcess.pid) {
          this.runningProcesses.set(childProcess.pid, childProcess);
        }

        // Handle stdout
        if (childProcess.stdout) {
          childProcess.stdout.on('data', (data) => {
            const chunk = data.toString();
            stdout += chunk;
            this.emit('stdout', { 
              sessionId: context.sessionId, 
              data: chunk, 
              command,
              pid: childProcess.pid 
            });
          });
        }

        // Handle stderr
        if (childProcess.stderr) {
          childProcess.stderr.on('data', (data) => {
            const chunk = data.toString();
            stderr += chunk;
            this.emit('stderr', { 
              sessionId: context.sessionId, 
              data: chunk, 
              command,
              pid: childProcess.pid 
            });
          });
        }

        // Handle process exit
        childProcess.on('close', (exitCode) => {
          if (isResolved) return;
          isResolved = true;

          const duration = Date.now() - startTime;
          
          if (childProcess.pid) {
            this.runningProcesses.delete(childProcess.pid);
          }

          const result: ShellExecutionResult = {
            success: exitCode === 0,
            message: exitCode === 0 
              ? `Command executed successfully: ${command}`
              : `Command failed with exit code ${exitCode}: ${command}`,
            data: {
              stdout: stdout.trim(),
              stderr: stderr.trim(),
              exitCode: exitCode || 0,
              command,
              workingDirectory: cwd,
              duration,
              pid: childProcess.pid,
            },
          };

          logger.info(`Command completed`, {
            command,
            exitCode,
            duration,
            stdoutLength: stdout.length,
            stderrLength: stderr.length,
          }, 'ShellOperations', context.sessionId);

          resolve(result);
        });

        // Handle process error
        childProcess.on('error', (error) => {
          if (isResolved) return;
          isResolved = true;

          const duration = Date.now() - startTime;
          
          if (childProcess.pid) {
            this.runningProcesses.delete(childProcess.pid);
          }

          logger.error(`Command error: ${command}`, error, 'ShellOperations', context.sessionId);

          resolve({
            success: false,
            message: `Command error: ${command}`,
            error: error.message,
            data: {
              stdout: stdout.trim(),
              stderr: stderr.trim(),
              exitCode: -1,
              command,
              workingDirectory: cwd,
              duration,
              pid: childProcess.pid,
            },
          });
        });

        // Set timeout if specified
        if (timeout > 0 && !detached) {
          setTimeout(() => {
            if (isResolved) return;
            isResolved = true;

            logger.warn(`Command timeout: ${command}`, { timeout }, 'ShellOperations', context.sessionId);

            // Kill the process
            if (childProcess.pid) {
              this.killProcess(childProcess.pid);
            }

            const duration = Date.now() - startTime;

            resolve({
              success: false,
              message: `Command timed out after ${timeout}ms: ${command}`,
              error: 'TIMEOUT',
              data: {
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: -1,
                command,
                workingDirectory: cwd,
                duration,
                pid: childProcess.pid,
              },
            });
          }, timeout);
        }

        // For detached processes, resolve immediately
        if (detached) {
          isResolved = true;
          resolve({
            success: true,
            message: `Detached command started: ${command}`,
            data: {
              stdout: '',
              stderr: '',
              exitCode: 0,
              command,
              workingDirectory: cwd,
              duration: Date.now() - startTime,
              pid: childProcess.pid,
            },
          });
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to execute command: ${command}`, error, 'ShellOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to execute command: ${command}`,
        error: errorMessage,
      };
    }
  }

  public async executeScript(
    script: string,
    context: ExecutionContext,
    options: ShellExecutionOptions & { interpreter?: string } = {}
  ): Promise<ShellExecutionResult> {
    try {
      const { interpreter = 'bash' } = options;
      
      // Create a temporary script file
      const fs = await import('fs-extra');
      const path = await import('path');
      const { nanoid } = await import('nanoid');
      
      const tempDir = path.join(context.workingDirectory, '.tmp');
      await fs.ensureDir(tempDir);
      
      const scriptId = nanoid();
      const scriptPath = path.join(tempDir, `script_${scriptId}.sh`);
      
      await fs.writeFile(scriptPath, script, 'utf-8');
      await fs.chmod(scriptPath, '755');

      logger.info(`Executing script`, { 
        interpreter, 
        scriptPath,
        scriptLength: script.length 
      }, 'ShellOperations', context.sessionId);

      const result = await this.executeCommand(
        `${interpreter} "${scriptPath}"`,
        context,
        options
      );

      // Clean up temporary file
      try {
        await fs.remove(scriptPath);
      } catch (error) {
        logger.warn(`Failed to clean up script file: ${scriptPath}`, error, 'ShellOperations', context.sessionId);
      }

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to execute script`, error, 'ShellOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to execute script`,
        error: errorMessage,
      };
    }
  }

  public killProcess(pid: number): boolean {
    try {
      const process = this.runningProcesses.get(pid);
      if (process) {
        process.kill('SIGTERM');
        this.runningProcesses.delete(pid);
        logger.info(`Process killed`, { pid }, 'ShellOperations');
        return true;
      }
      return false;
    } catch (error) {
      logger.error(`Failed to kill process`, { pid, error }, 'ShellOperations');
      return false;
    }
  }

  public killAllProcesses(): number {
    let killedCount = 0;
    for (const [pid, process] of this.runningProcesses) {
      try {
        process.kill('SIGTERM');
        killedCount++;
      } catch (error) {
        logger.warn(`Failed to kill process ${pid}`, error, 'ShellOperations');
      }
    }
    this.runningProcesses.clear();
    logger.info(`Killed ${killedCount} processes`, undefined, 'ShellOperations');
    return killedCount;
  }

  public getRunningProcesses(): Array<{ pid: number; command?: string; startTime?: Date; status?: string }> {
    return Array.from(this.runningProcesses.entries()).map(([pid, process]) => ({
      pid,
      command: (process as any).spawnargs?.join(' '),
      startTime: (process as any).startTime || new Date(),
      status: process.killed ? 'killed' : 'running',
    }));
  }

  public async getProcessInfo(pid: number): Promise<any> {
    try {
      const process = this.runningProcesses.get(pid);
      if (!process) {
        return null;
      }

      return {
        pid,
        command: (process as any).spawnargs?.join(' '),
        startTime: (process as any).startTime || new Date(),
        status: process.killed ? 'killed' : 'running',
        exitCode: process.exitCode,
        signalCode: process.signalCode,
        connected: process.connected,
        stdio: process.stdio?.length || 0,
      };
    } catch (error) {
      logger.error(`Failed to get process info for PID ${pid}`, error, 'ShellOperations');
      return null;
    }
  }

  public async executeCommandWithRealTimeOutput(
    command: string,
    context: ExecutionContext,
    options: ShellExecutionOptions & {
      onStdout?: (data: string) => void;
      onStderr?: (data: string) => void;
    } = {}
  ): Promise<ShellExecutionResult> {
    try {
      this.validateCommand(command, context);

      const {
        timeout = 30000,
        cwd = context.workingDirectory,
        env = { ...process.env, ...context.environment },
        shell = true,
        detached = false,
        stdio = 'pipe',
        onStdout,
        onStderr,
      } = options;

      logger.info(`Executing command with real-time output: ${command}`, {
        cwd,
        timeout,
        detached
      }, 'ShellOperations', context.sessionId);

      const startTime = Date.now();

      return new Promise((resolve) => {
        let stdout = '';
        let stderr = '';
        let isResolved = false;

        const [cmd, ...args] = this.parseCommand(command);

        const childProcess = spawn(cmd!, args, {
          cwd,
          env,
          shell,
          detached,
          stdio,
        });

        if (childProcess.pid) {
          this.runningProcesses.set(childProcess.pid, childProcess);
          (childProcess as any).startTime = new Date();
        }

        // Handle stdout with real-time callback
        if (childProcess.stdout) {
          childProcess.stdout.on('data', (data) => {
            const chunk = data.toString();
            stdout += chunk;

            if (onStdout) {
              onStdout(chunk);
            }

            this.emit('stdout', {
              sessionId: context.sessionId,
              data: chunk,
              command,
              pid: childProcess.pid
            });
          });
        }

        // Handle stderr with real-time callback
        if (childProcess.stderr) {
          childProcess.stderr.on('data', (data) => {
            const chunk = data.toString();
            stderr += chunk;

            if (onStderr) {
              onStderr(chunk);
            }

            this.emit('stderr', {
              sessionId: context.sessionId,
              data: chunk,
              command,
              pid: childProcess.pid
            });
          });
        }

        // Handle process completion
        childProcess.on('close', (exitCode) => {
          if (isResolved) return;
          isResolved = true;

          const duration = Date.now() - startTime;

          if (childProcess.pid) {
            this.runningProcesses.delete(childProcess.pid);
          }

          const result: ShellExecutionResult = {
            success: exitCode === 0,
            message: exitCode === 0
              ? `Command executed successfully: ${command}`
              : `Command failed with exit code ${exitCode}: ${command}`,
            data: {
              stdout: stdout.trim(),
              stderr: stderr.trim(),
              exitCode: exitCode || 0,
              command,
              workingDirectory: cwd,
              duration,
              pid: childProcess.pid,
            },
          };

          logger.info(`Command completed with real-time output`, {
            command,
            exitCode,
            duration,
            stdoutLength: stdout.length,
            stderrLength: stderr.length,
          }, 'ShellOperations', context.sessionId);

          resolve(result);
        });

        // Handle process error
        childProcess.on('error', (error) => {
          if (isResolved) return;
          isResolved = true;

          const duration = Date.now() - startTime;

          if (childProcess.pid) {
            this.runningProcesses.delete(childProcess.pid);
          }

          logger.error(`Command error with real-time output: ${command}`, error, 'ShellOperations', context.sessionId);

          resolve({
            success: false,
            message: `Command error: ${command}`,
            error: error.message,
            data: {
              stdout: stdout.trim(),
              stderr: stderr.trim(),
              exitCode: -1,
              command,
              workingDirectory: cwd,
              duration,
              pid: childProcess.pid,
            },
          });
        });

        // Set timeout if specified
        if (timeout > 0 && !detached) {
          setTimeout(() => {
            if (isResolved) return;
            isResolved = true;

            logger.warn(`Command timeout with real-time output: ${command}`, { timeout }, 'ShellOperations', context.sessionId);

            if (childProcess.pid) {
              this.killProcess(childProcess.pid);
            }

            const duration = Date.now() - startTime;

            resolve({
              success: false,
              message: `Command timed out after ${timeout}ms: ${command}`,
              error: 'TIMEOUT',
              data: {
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: -1,
                command,
                workingDirectory: cwd,
                duration,
                pid: childProcess.pid,
              },
            });
          }, timeout);
        }

        // For detached processes, resolve immediately
        if (detached) {
          isResolved = true;
          resolve({
            success: true,
            message: `Detached command started: ${command}`,
            data: {
              stdout: '',
              stderr: '',
              exitCode: 0,
              command,
              workingDirectory: cwd,
              duration: Date.now() - startTime,
              pid: childProcess.pid,
            },
          });
        }
      });
    } catch (error: any) {
      logger.error(`Failed to execute command with real-time output: ${command}`, error, 'ShellOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to execute command: ${command}`,
        error: error.message,
      };
    }
  }

  private validateCommand(command: string, _context: ExecutionContext): void {
    const cliConfig = config.getConfig();
    
    if (!cliConfig.tools.allowShellExecution) {
      throw new Error('Shell execution is disabled in configuration');
    }

    // Basic command validation
    if (!command || command.trim().length === 0) {
      throw new Error('Command cannot be empty');
    }

    // Check for dangerous commands (basic protection)
    const dangerousCommands = [
      'rm -rf /',
      'format',
      'del /f /s /q',
      'shutdown',
      'reboot',
      'halt',
      'poweroff',
    ];

    const lowerCommand = command.toLowerCase();
    for (const dangerous of dangerousCommands) {
      if (lowerCommand.includes(dangerous)) {
        throw new Error(`Dangerous command detected: ${command}`);
      }
    }
  }

  private parseCommand(command: string): string[] {
    // Simple command parsing - you might want to use a more sophisticated parser
    const args: string[] = [];
    let current = '';
    let inQuotes = false;
    let quoteChar = '';

    for (let i = 0; i < command.length; i++) {
      const char = command[i];
      
      if ((char === '"' || char === "'") && !inQuotes) {
        inQuotes = true;
        quoteChar = char;
      } else if (char === quoteChar && inQuotes) {
        inQuotes = false;
        quoteChar = '';
      } else if (char === ' ' && !inQuotes) {
        if (current.trim()) {
          args.push(current.trim());
          current = '';
        }
      } else {
        current += char;
      }
    }

    if (current.trim()) {
      args.push(current.trim());
    }

    return args;
  }
}

export const shellOperations = ShellOperations.getInstance();
