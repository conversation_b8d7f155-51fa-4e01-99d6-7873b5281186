import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';

import { ExecutionContext } from '@/types';
import { logger } from '@/utils/logger';
import { config } from '@/config';

export interface FileOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export class FileOperations {
  private static instance: FileOperations;

  public static getInstance(): FileOperations {
    if (!FileOperations.instance) {
      FileOperations.instance = new FileOperations();
    }
    return FileOperations.instance;
  }

  public async readFile(filePath: string, context: ExecutionContext): Promise<FileOperationResult> {
    try {
      this.validatePath(filePath, context);
      
      const absolutePath = this.resolvePath(filePath, context.workingDirectory);
      
      if (!fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `File does not exist: ${filePath}`,
          error: 'FILE_NOT_FOUND',
        };
      }

      const stat = await fs.stat(absolutePath);
      if (!stat.isFile()) {
        return {
          success: false,
          message: `Path is not a file: ${filePath}`,
          error: 'NOT_A_FILE',
        };
      }

      const content = await fs.readFile(absolutePath, 'utf-8');
      
      logger.debug(`File read successfully`, { filePath, size: content.length }, 'FileOperations', context.sessionId);
      
      return {
        success: true,
        message: `File read successfully: ${filePath}`,
        data: {
          content,
          size: stat.size,
          lastModified: stat.mtime,
          path: filePath,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to read file: ${filePath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to read file: ${filePath}`,
        error: errorMessage,
      };
    }
  }

  public async writeFile(
    filePath: string, 
    content: string, 
    context: ExecutionContext,
    options: { overwrite?: boolean; createDirs?: boolean } = {}
  ): Promise<FileOperationResult> {
    try {
      this.validatePath(filePath, context);
      
      const absolutePath = this.resolvePath(filePath, context.workingDirectory);
      const { overwrite = false, createDirs = true } = options;

      if (fs.existsSync(absolutePath) && !overwrite) {
        return {
          success: false,
          message: `File already exists: ${filePath}. Use overwrite option to replace.`,
          error: 'FILE_EXISTS',
        };
      }

      if (createDirs) {
        await fs.ensureDir(path.dirname(absolutePath));
      }

      await fs.writeFile(absolutePath, content, 'utf-8');
      
      const stat = await fs.stat(absolutePath);
      
      logger.info(`File written successfully`, { 
        filePath, 
        size: content.length,
        overwrite 
      }, 'FileOperations', context.sessionId);
      
      return {
        success: true,
        message: `File written successfully: ${filePath}`,
        data: {
          path: filePath,
          size: stat.size,
          created: !overwrite,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to write file: ${filePath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to write file: ${filePath}`,
        error: errorMessage,
      };
    }
  }

  public async deleteFile(filePath: string, context: ExecutionContext): Promise<FileOperationResult> {
    try {
      this.validatePath(filePath, context);

      const absolutePath = this.resolvePath(filePath, context.workingDirectory);

      if (!fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `File does not exist: ${filePath}`,
          error: 'FILE_NOT_FOUND',
        };
      }

      await fs.remove(absolutePath);

      logger.info(`File deleted successfully`, { filePath }, 'FileOperations', context.sessionId);

      return {
        success: true,
        message: `File deleted successfully: ${filePath}`,
        data: { path: filePath },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to delete file: ${filePath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to delete file: ${filePath}`,
        error: errorMessage,
      };
    }
  }

  public async copyFile(
    sourcePath: string, 
    destPath: string, 
    context: ExecutionContext,
    options: { overwrite?: boolean } = {}
  ): Promise<FileOperationResult> {
    try {
      this.validatePath(sourcePath, context);
      this.validatePath(destPath, context);
      
      const absoluteSource = this.resolvePath(sourcePath, context.workingDirectory);
      const absoluteDest = this.resolvePath(destPath, context.workingDirectory);
      const { overwrite = false } = options;

      if (!fs.existsSync(absoluteSource)) {
        return {
          success: false,
          message: `Source file does not exist: ${sourcePath}`,
          error: 'SOURCE_NOT_FOUND',
        };
      }

      if (fs.existsSync(absoluteDest) && !overwrite) {
        return {
          success: false,
          message: `Destination file already exists: ${destPath}. Use overwrite option to replace.`,
          error: 'DEST_EXISTS',
        };
      }

      await fs.copy(absoluteSource, absoluteDest, { overwrite });
      
      logger.info(`File copied successfully`, { 
        sourcePath, 
        destPath, 
        overwrite 
      }, 'FileOperations', context.sessionId);
      
      return {
        success: true,
        message: `File copied successfully: ${sourcePath} -> ${destPath}`,
        data: {
          sourcePath,
          destPath,
          overwrite,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to copy file: ${sourcePath} -> ${destPath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to copy file: ${sourcePath} -> ${destPath}`,
        error: errorMessage,
      };
    }
  }

  public async moveFile(
    sourcePath: string, 
    destPath: string, 
    context: ExecutionContext
  ): Promise<FileOperationResult> {
    try {
      this.validatePath(sourcePath, context);
      this.validatePath(destPath, context);
      
      const absoluteSource = this.resolvePath(sourcePath, context.workingDirectory);
      const absoluteDest = this.resolvePath(destPath, context.workingDirectory);

      if (!fs.existsSync(absoluteSource)) {
        return {
          success: false,
          message: `Source file does not exist: ${sourcePath}`,
          error: 'SOURCE_NOT_FOUND',
        };
      }

      if (fs.existsSync(absoluteDest)) {
        return {
          success: false,
          message: `Destination file already exists: ${destPath}`,
          error: 'DEST_EXISTS',
        };
      }

      await fs.move(absoluteSource, absoluteDest);
      
      logger.info(`File moved successfully`, { 
        sourcePath, 
        destPath 
      }, 'FileOperations', context.sessionId);
      
      return {
        success: true,
        message: `File moved successfully: ${sourcePath} -> ${destPath}`,
        data: {
          sourcePath,
          destPath,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to move file: ${sourcePath} -> ${destPath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to move file: ${sourcePath} -> ${destPath}`,
        error: errorMessage,
      };
    }
  }

  public async createDirectory(dirPath: string, context: ExecutionContext): Promise<FileOperationResult> {
    try {
      this.validatePath(dirPath, context);
      
      const absolutePath = this.resolvePath(dirPath, context.workingDirectory);
      
      if (fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `Directory already exists: ${dirPath}`,
          error: 'DIR_EXISTS',
        };
      }

      await fs.ensureDir(absolutePath);
      
      logger.info(`Directory created successfully`, { dirPath }, 'FileOperations', context.sessionId);
      
      return {
        success: true,
        message: `Directory created successfully: ${dirPath}`,
        data: { path: dirPath },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to create directory: ${dirPath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to create directory: ${dirPath}`,
        error: errorMessage,
      };
    }
  }

  public async listDirectory(dirPath: string, context: ExecutionContext): Promise<FileOperationResult> {
    try {
      this.validatePath(dirPath, context);

      const absolutePath = this.resolvePath(dirPath, context.workingDirectory);

      if (!fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `Directory does not exist: ${dirPath}`,
          error: 'DIR_NOT_FOUND',
        };
      }

      const stat = await fs.stat(absolutePath);
      if (!stat.isDirectory()) {
        return {
          success: false,
          message: `Path is not a directory: ${dirPath}`,
          error: 'NOT_A_DIRECTORY',
        };
      }

      const items = await fs.readdir(absolutePath, { withFileTypes: true });
      const result = await Promise.all(
        items.map(async (item) => {
          const itemPath = path.join(absolutePath, item.name);
          const itemStat = await fs.stat(itemPath);

          return {
            name: item.name,
            type: item.isDirectory() ? 'directory' : 'file',
            size: itemStat.size,
            lastModified: itemStat.mtime,
            permissions: {
              readable: !!(itemStat.mode & parseInt('400', 8)),
              writable: !!(itemStat.mode & parseInt('200', 8)),
              executable: !!(itemStat.mode & parseInt('100', 8)),
            },
          };
        })
      );

      logger.debug(`Directory listed successfully`, {
        dirPath,
        itemCount: result.length
      }, 'FileOperations', context.sessionId);

      return {
        success: true,
        message: `Directory listed successfully: ${dirPath}`,
        data: {
          path: dirPath,
          items: result,
          count: result.length,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to list directory: ${dirPath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to list directory: ${dirPath}`,
        error: errorMessage,
      };
    }
  }

  public async searchFiles(
    pattern: string,
    context: ExecutionContext,
    options: {
      directory?: string;
      includeContent?: boolean;
      maxResults?: number;
      fileTypes?: string[];
    } = {}
  ): Promise<FileOperationResult> {
    try {
      const {
        directory = '.',
        includeContent = false,
        maxResults = 100,
        fileTypes = [],
      } = options;

      this.validatePath(directory, context);

      const searchDir = this.resolvePath(directory, context.workingDirectory);

      if (!fs.existsSync(searchDir)) {
        return {
          success: false,
          message: `Search directory does not exist: ${directory}`,
          error: 'DIR_NOT_FOUND',
        };
      }

      const excludePatterns = config.getConfig().context.excludePatterns;
      let globPattern = pattern;

      // Add file type filter if specified
      if (fileTypes.length > 0) {
        const extensions = fileTypes.map(ext => ext.startsWith('.') ? ext : `.${ext}`);
        globPattern = `**/*{${extensions.join(',')}}`;
      }

      const files = await glob(globPattern, {
        cwd: searchDir,
        ignore: excludePatterns,
        absolute: false,
        nodir: true,
      });

      const results = [];
      for (const file of files.slice(0, maxResults)) {
        const filePath = path.join(searchDir, file);
        const stat = await fs.stat(filePath);

        const result: any = {
          path: file,
          size: stat.size,
          lastModified: stat.mtime,
          extension: path.extname(file),
        };

        if (includeContent && stat.size < 100000) { // Only read files < 100KB
          try {
            result.content = await fs.readFile(filePath, 'utf-8');
          } catch (error) {
            result.contentError = 'Failed to read content';
          }
        }

        results.push(result);
      }

      logger.debug(`File search completed`, {
        pattern,
        directory,
        resultCount: results.length
      }, 'FileOperations', context.sessionId);

      return {
        success: true,
        message: `Found ${results.length} files matching pattern: ${pattern}`,
        data: {
          pattern,
          directory,
          results,
          totalFound: files.length,
          truncated: files.length > maxResults,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to search files: ${pattern}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to search files: ${pattern}`,
        error: errorMessage,
      };
    }
  }

  public async grepFiles(
    searchText: string,
    context: ExecutionContext,
    options: {
      directory?: string;
      filePattern?: string;
      caseSensitive?: boolean;
      maxResults?: number;
      contextLines?: number;
    } = {}
  ): Promise<FileOperationResult> {
    try {
      const {
        directory = '.',
        filePattern = '**/*',
        caseSensitive = false,
        maxResults = 100,
        contextLines = 2,
      } = options;

      this.validatePath(directory, context);

      const searchDir = this.resolvePath(directory, context.workingDirectory);

      if (!fs.existsSync(searchDir)) {
        return {
          success: false,
          message: `Search directory does not exist: ${directory}`,
          error: 'DIR_NOT_FOUND',
        };
      }

      const excludePatterns = config.getConfig().context.excludePatterns;
      const files = await glob(filePattern, {
        cwd: searchDir,
        ignore: excludePatterns,
        absolute: false,
        nodir: true,
      });

      const results = [];
      const regex = new RegExp(searchText, caseSensitive ? 'g' : 'gi');

      for (const file of files) {
        if (results.length >= maxResults) break;

        const filePath = path.join(searchDir, file);
        const stat = await fs.stat(filePath);

        // Skip large files
        if (stat.size > 1024 * 1024) continue; // Skip files > 1MB

        try {
          const content = await fs.readFile(filePath, 'utf-8');
          const lines = content.split('\n');
          const matches = [];

          for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line && regex.test(line)) {
              const start = Math.max(0, i - contextLines);
              const end = Math.min(lines.length - 1, i + contextLines);

              matches.push({
                lineNumber: i + 1,
                line,
                context: {
                  before: lines.slice(start, i),
                  after: lines.slice(i + 1, end + 1),
                },
              });
            }
          }

          if (matches.length > 0) {
            results.push({
              file,
              matches,
              matchCount: matches.length,
            });
          }
        } catch (error) {
          // Skip files that can't be read as text
          continue;
        }
      }

      logger.debug(`Grep search completed`, {
        searchText,
        directory,
        fileCount: files.length,
        resultCount: results.length
      }, 'FileOperations', context.sessionId);

      return {
        success: true,
        message: `Found ${results.length} files with matches for: ${searchText}`,
        data: {
          searchText,
          directory,
          results,
          totalMatches: results.reduce((sum, r) => sum + r.matchCount, 0),
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to grep files: ${searchText}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to grep files: ${searchText}`,
        error: errorMessage,
      };
    }
  }

  public async getFileInfo(filePath: string, context: ExecutionContext): Promise<FileOperationResult> {
    try {
      this.validatePath(filePath, context);

      const absolutePath = this.resolvePath(filePath, context.workingDirectory);

      if (!fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `File does not exist: ${filePath}`,
          error: 'FILE_NOT_FOUND',
        };
      }

      const stat = await fs.stat(absolutePath);

      return {
        success: true,
        message: `File info retrieved: ${filePath}`,
        data: {
          path: filePath,
          absolutePath,
          size: stat.size,
          isFile: stat.isFile(),
          isDirectory: stat.isDirectory(),
          lastModified: stat.mtime,
          lastAccessed: stat.atime,
          created: stat.birthtime,
          permissions: {
            readable: !!(stat.mode & parseInt('400', 8)),
            writable: !!(stat.mode & parseInt('200', 8)),
            executable: !!(stat.mode & parseInt('100', 8)),
            mode: stat.mode.toString(8),
          },
          extension: path.extname(filePath),
          basename: path.basename(filePath),
          dirname: path.dirname(filePath),
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to get file info: ${filePath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to get file info: ${filePath}`,
        error: errorMessage,
      };
    }
  }

  public async setPermissions(
    filePath: string,
    permissions: string | number,
    context: ExecutionContext
  ): Promise<FileOperationResult> {
    try {
      this.validatePath(filePath, context);

      const absolutePath = this.resolvePath(filePath, context.workingDirectory);

      if (!fs.existsSync(absolutePath)) {
        return {
          success: false,
          message: `File does not exist: ${filePath}`,
          error: 'FILE_NOT_FOUND',
        };
      }

      await fs.chmod(absolutePath, permissions);

      logger.info(`Permissions changed successfully`, {
        filePath,
        permissions
      }, 'FileOperations', context.sessionId);

      return {
        success: true,
        message: `Permissions changed successfully: ${filePath}`,
        data: {
          path: filePath,
          permissions,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Failed to change permissions: ${filePath}`, error, 'FileOperations', context.sessionId);
      return {
        success: false,
        message: `Failed to change permissions: ${filePath}`,
        error: errorMessage,
      };
    }
  }

  private validatePath(filePath: string, context: ExecutionContext): void {
    const cliConfig = config.getConfig();

    if (!cliConfig.tools.allowFileOperations) {
      throw new Error('File operations are disabled in configuration');
    }

    // Check for restricted paths
    const absolutePath = this.resolvePath(filePath, context.workingDirectory);

    for (const restrictedPath of cliConfig.tools.restrictedPaths) {
      const resolvedRestricted = path.resolve(restrictedPath);
      if (absolutePath.startsWith(resolvedRestricted)) {
        throw new Error(`Access to restricted path: ${filePath}`);
      }
    }

    // Prevent path traversal attacks
    if (filePath.includes('..') || filePath.includes('~')) {
      throw new Error(`Invalid path: ${filePath}`);
    }
  }

  private resolvePath(filePath: string, workingDirectory: string): string {
    if (path.isAbsolute(filePath)) {
      return path.normalize(filePath);
    }
    return path.resolve(workingDirectory, filePath);
  }
}

export const fileOperations = FileOperations.getInstance();
